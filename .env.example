# 智慧食堂平台环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# 数据库配置
DATABASE_URL=mssql+pyodbc://username:password@server/database?driver=ODBC+Driver+17+for+SQL+Server

# Flask 配置
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_DEBUG=True

# 系统信息配置
SYSTEM_NAME=智慧食堂平台
SYSTEM_DESCRIPTION=专业校园餐饮管理解决方案
SYSTEM_VERSION=1.0.0
COMPANY_NAME=校园餐智慧食堂
COMPANY_WEBSITE=https://scmmp.com

# 联系信息配置
SUPPORT_PHONE=18373062333
SUPPORT_EMAIL=<EMAIL>
SUPPORT_HOURS=工作日 9:00-18:00
EMAIL_RESPONSE_TIME=24小时内回复

# 默认设置配置
DEFAULT_PROJECT_NAME=初中毕业生学生去向管理系统
DEFAULT_THEME_COLOR=primary
DEFAULT_LOGO_PATH=/static/uploads/system/logo_20250601185000.png
DEFAULT_ITEMS_PER_PAGE=10

# 文件路径配置
UPLOAD_FOLDER=app/static/uploads
LOG_FOLDER=logs
TEMP_FOLDER=temp
BACKUP_FOLDER=backups

# 网络配置
DEFAULT_HOST=127.0.0.1
DEFAULT_PORT=8080
ALLOWED_HOSTS=localhost,127.0.0.1,xiaoyuanst.com

# 安全配置
MAX_LOGIN_ATTEMPTS=5
SESSION_TIMEOUT=3600
PASSWORD_MIN_LENGTH=6
CSRF_TIMEOUT=3600
SECURITY_WHITELIST_IPS=127.0.0.1,::1,localhost

# 业务配置
MAX_FILE_SIZE=16777216
SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,gif,webp
SUPPORTED_DOCUMENT_FORMATS=pdf,doc,docx,xls,xlsx

# API配置
API_RATE_LIMIT=60
API_TIMEOUT=30
API_MAX_PAGE_SIZE=100

# IIS配置（Windows部署）
SITE_NAME=xiaoyuanst.com
SITE_PORT=80
SITE_PATH=C:\StudentsCMSSP
APP_POOL_NAME=xiaoyuanst.com_AppPool
WEB_CONFIG_PATH=C:\StudentsCMSSP\web.config

# 财务系统配置
FINANCIAL_DECIMAL_PLACES=2
FINANCIAL_CURRENCY_SYMBOL=¥
FINANCIAL_TOLERANCE=0.01

# Beancount 配置
BEANCOUNT_ENABLED=True
BEANCOUNT_CURRENCY=CNY

# 报表配置
REPORTS_TEMP_DIR=temp/reports
REPORTS_MAX_EXPORT_ROWS=10000

# 加密配置
ENCRYPTION_KEY=your-encryption-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/financial.log

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# CDN配置
USE_CDN=true
