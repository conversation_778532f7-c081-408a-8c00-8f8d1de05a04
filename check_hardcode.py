#!/usr/bin/env python3
"""
硬编码检查脚本
用于检查项目中可能存在的硬编码问题
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple

class HardcodeChecker:
    """硬编码检查器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.issues = []
        
        # 硬编码模式定义
        self.patterns = {
            'ip_addresses': [
                (r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b', 'IP地址'),
                (r'\blocalhost\b', 'localhost'),
                (r'\b127\.0\.0\.1\b', '本地回环地址'),
            ],
            'urls': [
                (r'https?://[^\s\'"]+', 'HTTP/HTTPS URL'),
                (r'ftp://[^\s\'"]+', 'FTP URL'),
            ],
            'email_addresses': [
                (r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '邮箱地址'),
            ],
            'phone_numbers': [
                (r'\b1[3-9]\d{9}\b', '手机号码'),
                (r'\b\d{3,4}-\d{7,8}\b', '固定电话'),
            ],
            'file_paths': [
                (r'[C-Z]:\\[^\s\'"]+', 'Windows绝对路径'),
                (r'/[^\s\'"]*(?:/[^\s\'"]+)+', 'Unix绝对路径'),
            ],
            'database_connections': [
                (r'mssql\+pyodbc://[^\s\'"]+', 'SQL Server连接字符串'),
                (r'mysql://[^\s\'"]+', 'MySQL连接字符串'),
                (r'postgresql://[^\s\'"]+', 'PostgreSQL连接字符串'),
                (r'sqlite:///[^\s\'"]+', 'SQLite连接字符串'),
            ],
            'passwords_keys': [
                (r'password\s*=\s*["\'][^"\']+["\']', '密码'),
                (r'secret\s*=\s*["\'][^"\']+["\']', '密钥'),
                (r'key\s*=\s*["\'][^"\']+["\']', '密钥'),
                (r'token\s*=\s*["\'][^"\']+["\']', '令牌'),
            ],
            'ports': [
                (r':\d{4,5}\b', '端口号'),
            ],
        }
        
        # 排除的文件和目录
        self.exclude_patterns = [
            r'\.git',
            r'__pycache__',
            r'\.pyc$',
            r'node_modules',
            r'venv',
            r'\.env',
            r'\.log$',
            r'\.backup',
            r'check_hardcode\.py$',  # 排除自身
            r'HARDCODE_FIXES\.md$',  # 排除文档
            r'hardcode_report\.txt$',  # 排除报告文件
            r'static/vendor',  # 排除第三方库
            r'static/bootstrap',  # 排除Bootstrap文件
            r'\.min\.',  # 排除压缩文件
            r'migrations',  # 排除数据库迁移文件
            r'logs',  # 排除日志文件
            r'scripts/init_demo_data\.py$',  # 排除演示数据脚本
        ]
        
        # 需要检查的文件扩展名
        self.include_extensions = [
            '.py', '.html', '.js', '.css', '.ps1', '.sh', '.bat', '.cmd',
            '.json', '.yaml', '.yml', '.xml', '.ini', '.cfg', '.conf'
        ]
    
    def should_exclude(self, file_path: Path) -> bool:
        """检查文件是否应该被排除"""
        file_str = str(file_path)
        for pattern in self.exclude_patterns:
            if re.search(pattern, file_str):
                return True
        return False
    
    def should_include(self, file_path: Path) -> bool:
        """检查文件是否应该被包含"""
        return file_path.suffix.lower() in self.include_extensions
    
    def check_file(self, file_path: Path) -> List[Dict]:
        """检查单个文件"""
        file_issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
                
                for line_num, line in enumerate(lines, 1):
                    for category, patterns in self.patterns.items():
                        for pattern, description in patterns:
                            matches = re.finditer(pattern, line, re.IGNORECASE)
                            for match in matches:
                                # 跳过一些明显的注释或示例
                                if self.is_likely_comment_or_example(line, match.group()):
                                    continue
                                
                                file_issues.append({
                                    'file': str(file_path.relative_to(self.project_root)),
                                    'line': line_num,
                                    'category': category,
                                    'description': description,
                                    'content': line.strip(),
                                    'match': match.group(),
                                    'severity': self.get_severity(category, match.group())
                                })
        except Exception as e:
            print(f"警告：无法读取文件 {file_path}: {e}")
        
        return file_issues
    
    def is_likely_comment_or_example(self, line: str, match: str) -> bool:
        """判断是否可能是注释或示例"""
        line_lower = line.lower().strip()
        
        # 跳过注释行
        if line_lower.startswith('#') or line_lower.startswith('//'):
            return True
        
        # 跳过包含"示例"、"example"等词的行
        if any(word in line_lower for word in ['示例', 'example', 'sample', 'demo', 'test']):
            return True
        
        # 跳过明显的占位符
        if any(placeholder in match.lower() for placeholder in ['example', 'placeholder', 'your-', 'xxx']):
            return True
        
        return False
    
    def get_severity(self, category: str, match: str) -> str:
        """获取问题严重程度"""
        if category in ['passwords_keys', 'database_connections']:
            return 'HIGH'
        elif category in ['ip_addresses', 'file_paths']:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def scan_project(self) -> List[Dict]:
        """扫描整个项目"""
        print(f"🔍 开始扫描项目: {self.project_root}")
        
        all_issues = []
        file_count = 0
        
        for root, dirs, files in os.walk(self.project_root):
            # 排除目录
            dirs[:] = [d for d in dirs if not self.should_exclude(Path(root) / d)]
            
            for file in files:
                file_path = Path(root) / file
                
                if self.should_exclude(file_path) or not self.should_include(file_path):
                    continue
                
                file_count += 1
                file_issues = self.check_file(file_path)
                all_issues.extend(file_issues)
        
        print(f"📊 扫描完成: 检查了 {file_count} 个文件，发现 {len(all_issues)} 个潜在问题")
        return all_issues
    
    def generate_report(self, issues: List[Dict]) -> str:
        """生成报告"""
        if not issues:
            return "✅ 未发现硬编码问题！"
        
        # 按严重程度分组
        severity_groups = {'HIGH': [], 'MEDIUM': [], 'LOW': []}
        for issue in issues:
            severity_groups[issue['severity']].append(issue)
        
        report = []
        report.append("🚨 硬编码检查报告")
        report.append("=" * 50)
        
        for severity in ['HIGH', 'MEDIUM', 'LOW']:
            if not severity_groups[severity]:
                continue
            
            severity_names = {'HIGH': '高危', 'MEDIUM': '中等', 'LOW': '低危'}
            report.append(f"\n📍 {severity_names[severity]}问题 ({len(severity_groups[severity])} 个):")
            report.append("-" * 30)
            
            # 按文件分组
            file_groups = {}
            for issue in severity_groups[severity]:
                file_path = issue['file']
                if file_path not in file_groups:
                    file_groups[file_path] = []
                file_groups[file_path].append(issue)
            
            for file_path, file_issues in file_groups.items():
                report.append(f"\n📄 {file_path}:")
                for issue in file_issues:
                    report.append(f"  第{issue['line']}行: {issue['description']} - {issue['match']}")
                    report.append(f"    内容: {issue['content']}")
        
        return "\n".join(report)
    
    def save_report(self, issues: List[Dict], output_file: str = "hardcode_report.txt"):
        """保存报告到文件"""
        report = self.generate_report(issues)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📝 报告已保存到: {output_file}")

def main():
    """主函数"""
    project_root = sys.argv[1] if len(sys.argv) > 1 else "."
    
    checker = HardcodeChecker(project_root)
    issues = checker.scan_project()
    
    # 打印报告
    print("\n" + checker.generate_report(issues))
    
    # 保存报告
    checker.save_report(issues)
    
    # 返回适当的退出码
    if issues:
        high_issues = [i for i in issues if i['severity'] == 'HIGH']
        if high_issues:
            print(f"\n❌ 发现 {len(high_issues)} 个高危问题，需要立即处理！")
            return 1
        else:
            print(f"\n⚠️  发现 {len(issues)} 个问题，建议处理。")
            return 0
    else:
        print("\n✅ 项目检查通过！")
        return 0

if __name__ == "__main__":
    sys.exit(main())
