"""
系统常量配置文件
用于管理项目中的硬编码常量，便于统一管理和修改
"""

import os
from typing import Dict, Any

class SystemConstants:
    """系统常量类"""
    
    # 联系信息
    CONTACT_INFO = {
        'support_phone': os.environ.get('SUPPORT_PHONE', '18373062333'),
        'support_email': os.environ.get('SUPPORT_EMAIL', '<EMAIL>'),
        'support_hours': os.environ.get('SUPPORT_HOURS', '工作日 9:00-18:00'),
        'email_response_time': os.environ.get('EMAIL_RESPONSE_TIME', '24小时内回复'),
    }
    
    # 系统信息
    SYSTEM_INFO = {
        'name': os.environ.get('SYSTEM_NAME', '智慧食堂平台'),
        'description': os.environ.get('SYSTEM_DESCRIPTION', '专业校园餐饮管理解决方案'),
        'version': os.environ.get('SYSTEM_VERSION', '1.0.0'),
        'company': os.environ.get('COMPANY_NAME', '校园餐智慧食堂'),
        'website': os.environ.get('COMPANY_WEBSITE', 'https://scmmp.com'),
    }
    
    # 默认配置
    DEFAULT_SETTINGS = {
        'project_name': os.environ.get('DEFAULT_PROJECT_NAME', '初中毕业生学生去向管理系统'),
        'theme_color': os.environ.get('DEFAULT_THEME_COLOR', 'primary'),
        'logo_path': os.environ.get('DEFAULT_LOGO_PATH', '/static/uploads/system/logo_20250601185000.png'),
        'items_per_page': int(os.environ.get('DEFAULT_ITEMS_PER_PAGE', '10')),
    }
    
    # 文件路径配置
    FILE_PATHS = {
        'upload_folder': os.environ.get('UPLOAD_FOLDER', 'app/static/uploads'),
        'log_folder': os.environ.get('LOG_FOLDER', 'logs'),
        'temp_folder': os.environ.get('TEMP_FOLDER', 'temp'),
        'backup_folder': os.environ.get('BACKUP_FOLDER', 'backups'),
    }
    
    # 网络配置
    NETWORK_CONFIG = {
        'default_host': os.environ.get('DEFAULT_HOST', '127.0.0.1'),
        'default_port': int(os.environ.get('DEFAULT_PORT', '8080')),
        'allowed_hosts': os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1,xiaoyuanst.com').split(','),
    }
    
    # 安全配置
    SECURITY_CONFIG = {
        'max_login_attempts': int(os.environ.get('MAX_LOGIN_ATTEMPTS', '5')),
        'session_timeout': int(os.environ.get('SESSION_TIMEOUT', '3600')),  # 秒
        'password_min_length': int(os.environ.get('PASSWORD_MIN_LENGTH', '6')),
        'csrf_timeout': int(os.environ.get('CSRF_TIMEOUT', '3600')),  # 秒
    }
    
    # 业务配置
    BUSINESS_CONFIG = {
        'max_file_size': int(os.environ.get('MAX_FILE_SIZE', '16777216')),  # 16MB
        'supported_image_formats': os.environ.get('SUPPORTED_IMAGE_FORMATS', 'jpg,jpeg,png,gif,webp').split(','),
        'supported_document_formats': os.environ.get('SUPPORTED_DOCUMENT_FORMATS', 'pdf,doc,docx,xls,xlsx').split(','),
    }
    
    # API配置
    API_CONFIG = {
        'rate_limit_per_minute': int(os.environ.get('API_RATE_LIMIT', '60')),
        'timeout_seconds': int(os.environ.get('API_TIMEOUT', '30')),
        'max_page_size': int(os.environ.get('API_MAX_PAGE_SIZE', '100')),
    }
    
    @classmethod
    def get_contact_info(cls) -> Dict[str, str]:
        """获取联系信息"""
        return cls.CONTACT_INFO.copy()
    
    @classmethod
    def get_system_info(cls) -> Dict[str, str]:
        """获取系统信息"""
        return cls.SYSTEM_INFO.copy()
    
    @classmethod
    def get_default_settings(cls) -> Dict[str, Any]:
        """获取默认设置"""
        return cls.DEFAULT_SETTINGS.copy()
    
    @classmethod
    def get_file_paths(cls) -> Dict[str, str]:
        """获取文件路径配置"""
        return cls.FILE_PATHS.copy()
    
    @classmethod
    def get_network_config(cls) -> Dict[str, Any]:
        """获取网络配置"""
        return cls.NETWORK_CONFIG.copy()
    
    @classmethod
    def get_security_config(cls) -> Dict[str, int]:
        """获取安全配置"""
        return cls.SECURITY_CONFIG.copy()
    
    @classmethod
    def get_business_config(cls) -> Dict[str, Any]:
        """获取业务配置"""
        return cls.BUSINESS_CONFIG.copy()
    
    @classmethod
    def get_api_config(cls) -> Dict[str, int]:
        """获取API配置"""
        return cls.API_CONFIG.copy()
    
    @classmethod
    def get_all_constants(cls) -> Dict[str, Dict[str, Any]]:
        """获取所有常量配置"""
        return {
            'contact_info': cls.get_contact_info(),
            'system_info': cls.get_system_info(),
            'default_settings': cls.get_default_settings(),
            'file_paths': cls.get_file_paths(),
            'network_config': cls.get_network_config(),
            'security_config': cls.get_security_config(),
            'business_config': cls.get_business_config(),
            'api_config': cls.get_api_config(),
        }

# 创建全局常量实例
constants = SystemConstants()

# 便捷函数
def get_contact_info() -> Dict[str, str]:
    """获取联系信息的便捷函数"""
    return constants.get_contact_info()

def get_system_info() -> Dict[str, str]:
    """获取系统信息的便捷函数"""
    return constants.get_system_info()

def get_support_phone() -> str:
    """获取技术支持电话"""
    return constants.CONTACT_INFO['support_phone']

def get_support_email() -> str:
    """获取技术支持邮箱"""
    return constants.CONTACT_INFO['support_email']

def get_system_name() -> str:
    """获取系统名称"""
    return constants.SYSTEM_INFO['name']

def get_default_host_port() -> tuple:
    """获取默认主机和端口"""
    config = constants.get_network_config()
    return config['default_host'], config['default_port']
